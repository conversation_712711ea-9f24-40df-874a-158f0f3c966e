import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Image, Text, View, StyleSheet } from 'react-native';
import { colors, spacing, typography } from '../styles';
import { TabParamList } from '../types/navigation';
import { SafeAreaView } from 'react-native-safe-area-context';

// 导入页面组件
// import HomeScreen from '../screens/home/<USER>';
import RecordingScreen from '../screens/recording/RecordingScreen';
import { KnowledgeScreen } from '../screens/knowledge/KnowledgeScreen';
import { WritingScreen } from '../screens/writing/WritingScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';

const Tab = createBottomTabNavigator<TabParamList>();

// 公共头部组件
const CommonHeader: React.FC<{ title: string }> = ({ title }) => (
  <SafeAreaView style={headerStyles.safeArea} edges={['top']}>
    <View style={headerStyles.container}>
      <Text style={headerStyles.title}>{title}</Text>
    </View>
  </SafeAreaView>
);

export const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => {
          let iconSource;

          switch (route.name) {
            case 'RecordingTab':
              iconSource = focused
                ? require('../../assets/images/navigation/tab-recording-active.png')
                : require('../../assets/images/navigation/tab-recording-inactive.png');
              break;
            case 'KnowledgeTab':
              iconSource = focused
                ? require('../../assets/images/navigation/tab-knowledge-active.png')
                : require('../../assets/images/navigation/tab-knowledge.png');
              break;
            case 'WritingTab':
              iconSource = focused
                ? require('../../assets/images/navigation/tab-writing-active.png')
                : require('../../assets/images/navigation/tab-writing-inactive.png');
              break;
            case 'ProfileTab':
              iconSource = focused
                ? require('../../assets/images/navigation/tab-profile-active.png')
                : require('../../assets/images/navigation/tab-profile-inactive.png');
              break;
            default:
              iconSource = require('../../assets/images/navigation/tab-recording-active.png');
          }

          // 关键：使用最简单的Image组件，模仿字体图标的渲染方式
          return (
            <Image
              source={iconSource}
              style={{ width: 20, height: 20 }}
              resizeMode="contain"
            />
          );
        },
        tabBarLabel: ({ focused }) => {
          // 重要技术说明：在React Navigation的tabBarLabel中使用Image组件会触发底部导航栏的下沉动画效果
          // 因此这里使用纯Text组件，通过颜色和字重变化来区分激活状态，避免下沉动画问题
          // 未来优化方向：研究其他渐变文字实现方案（如MaskedView、SVG等）

          // 获取标签文字
          let label;
          switch (route.name) {
            case 'RecordingTab':
              label = '录音';
              break;
            case 'KnowledgeTab':
              label = '知识';
              break;
            case 'WritingTab':
              label = '写作';
              break;
            case 'ProfileTab':
              label = '我的';
              break;
            default:
              label = '';
          }

          // 最终方案：激活和非激活都使用Text，用不同颜色区分
          // 这样确保没有下沉动画，同时保持良好的视觉效果
          return (
            <Text
              style={{
                fontSize: 10,
                color: focused ? '#007AFF' : '#B0B2BF', // 激活时用系统蓝色，非激活时用灰色
                textAlign: 'center',
                fontWeight: focused ? '600' : '400', // 激活时加粗，增强视觉反馈
              }}
            >
              {label}
            </Text>
          );
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          backgroundColor: '#FFFFFF', // 设计稿中的白色背景
          borderTopLeftRadius: 16, // 顶部圆角
          borderTopRightRadius: 16,
          borderTopWidth: 0, // 移除顶部边框
          paddingBottom: 8, // 增加底部内边距，给文字更多空间
          paddingTop: 6, // 增加顶部内边距
          height: 56, // 增加高度以容纳图标和文字
          elevation: 8,
          shadowColor: '#646778', // 设计稿中的阴影颜色
          shadowOffset: {
            width: 0,
            height: -1,
          },
          shadowOpacity: 0.08,
          shadowRadius: 2,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        },
        tabBarItemStyle: {
          paddingVertical: 4, // 增加垂直内边距，给文字更多空间
          paddingHorizontal: 16, // 设计稿中的水平内边距
          gap: 4, // 增加图标和文字间距
          justifyContent: 'center', // 确保内容垂直居中
          alignItems: 'center', // 确保内容水平居中
        },
        tabBarShowLabel: true, // 确保标签显示
        tabBarHideOnKeyboard: false, // 键盘显示时不隐藏导航栏
        tabBarBadgeStyle: {}, // 禁用默认的badge样式
        headerStyle: {
          backgroundColor: colors.background.primary,
          borderBottomColor: colors.separator,
        },
        headerTintColor: colors.text.primary,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
      })}
    >
      {/* <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: '首页',
          headerShown: false,
        }}
      /> */}
      <Tab.Screen
        name="RecordingTab"
        component={RecordingScreen}
        options={{
          title: '录音',
          header: () => <CommonHeader title="录音" />,
        }}
      />
      <Tab.Screen
        name="KnowledgeTab"
        component={KnowledgeScreen}
        options={{
          title: '知识',
          headerShown: false, // 暂时保持KnowledgeScreen自己的头部实现
        }}
      />
      <Tab.Screen
        name="WritingTab"
        component={WritingScreen}
        options={{
          title: 'AI写作',
          header: () => <CommonHeader title="AI 写作" />,
        }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileScreen}
        options={{
          title: '我的',
          headerShown: false, // 我的页面不需要导航头部
        }}
      />
    </Tab.Navigator>
  );
};

// 统一的头部样式
const headerStyles = StyleSheet.create({
  safeArea: {
    backgroundColor: colors.background.bg,
  },

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg, // 只设置顶部padding，与知识空间保持一致
    backgroundColor: colors.background.bg,
  },

  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
});
