import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Image,
  Modal,
  StatusBar,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '@/styles';
import ChatContent from './components/ChatContent';
import { useKnowledgeAIChatStore } from './stores/knowledgeAIChatStore';

// 配置接口定义
export interface AIChatConfig {
  contextType: 'knowledge' | 'writing';
  welcomeConfig: {
    title: string;
    description: string;
    presetQuestions: string[];
  };
  inputConfig: {
    placeholder: string;
  };
  contextParams: {
    paramName: string; // 'knowledgeId' | 'articleId'
    selector: string; // 'KnowledgeSelector' | 'ArticleSelector'
  };
}

interface CommonAIChatModalProps {
  visible: boolean;
  onClose: () => void;
  config: AIChatConfig;
}

// 🎯 强制状态栏高度方案：在Modal内部StatusBar API可能失效，使用固定值
const getStatusBarHeight = () => {
  if (Platform.OS === 'ios') {
    // iOS设备的状态栏高度（刘海屏通常是47px，普通屏幕44px）
    return 47; // 使用较大值确保覆盖所有iOS设备
  } else {
    // Android设备状态栏高度（模拟器通常24-48px，真机可能更大）
    return StatusBar.currentHeight || 48; // 使用较大的fallback值，确保完全覆盖
  }
};

const CommonAIChatModal: React.FC<CommonAIChatModalProps> = ({
  visible,
  onClose,
  config: _config,
}) => {
  const insets = useSafeAreaInsets(); // 获取安全区域信息
  const {
    createSession,
    sessions,
    setCurrentSession,
    currentSession,
    isCreatingSession,
  } = useKnowledgeAIChatStore();

  // 🎯 调试Modal对安全区域的影响
  const debugStatusBarHeight = getStatusBarHeight();
  React.useEffect(() => {
    if (visible) {
      console.log('🔍 安全区域调试信息:', {
        platform: Platform.OS,
        statusBarHeight: StatusBar.currentHeight,
        getStatusBarHeightResult: debugStatusBarHeight,
        safeAreaInsets: insets,
        modalVisible: visible,
        finalMarginTop: Math.max(insets.top, debugStatusBarHeight),
      });
    }
  }, [visible, debugStatusBarHeight, insets]);

  // 用于防止重复创建会话的标记
  const [hasTriedCreateSession, setHasTriedCreateSession] = useState(false);

  // Tab栏高度设置，与KnowledgeScreen保持一致
  const TAB_BAR_HEIGHT = 56;

  // 处理新建会话
  const handleCreateNewSession = async () => {
    // 清除当前会话，这会重置到欢迎界面
    setCurrentSession(null);
    // 然后创建新会话
    await createSession('新的知识问答会话');
  };

  // 处理历史会话按钮点击
  const handleHistorySession = () => {
    // 按时间排序的会话列表（最新的在前）
    const sortedSessions = [...sessions].sort(
      (a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );

    // 找到当前会话的索引
    const currentIndex = sortedSessions.findIndex(
      (s) => s.id === currentSession?.id
    );

    // 如果有下一个会话，切换到它；否则切换到第一个会话
    const nextSession = sortedSessions[currentIndex + 1] || sortedSessions[0];

    if (nextSession && nextSession.id !== currentSession?.id) {
      setCurrentSession(nextSession);
    }
  };

  // 重置hasTriedCreateSession当Modal关闭时，并清理SSE连接
  useEffect(() => {
    if (!visible) {
      setHasTriedCreateSession(false);
      // 🎯 Modal关闭时，强制清理所有SSE连接
      const { currentEventSource, stopStreaming } =
        useKnowledgeAIChatStore.getState();
      if (currentEventSource) {
        console.log('🧹 Modal关闭，清理SSE连接');
        stopStreaming();
      }
    }
  }, [visible]);

  // 自动创建会话逻辑（防止无限重试）
  useEffect(() => {
    // 当模态框打开且没有当前会话且没有试过创建时，自动创建一个会话
    if (
      visible &&
      !currentSession &&
      !isCreatingSession &&
      !hasTriedCreateSession
    ) {
      console.log('🚀 自动创建知识问答会话');
      setHasTriedCreateSession(true); // 立即标记为已试过
      createSession('知识问答会话').catch((error) => {
        console.error('❌ 自动创建会话失败:', error);
        // 失败后不再重试，需要用户手动重试或重新打开Modal
      });
    }
  }, [
    visible,
    currentSession,
    isCreatingSession,
    hasTriedCreateSession,
    createSession,
  ]);

  // 调试日志 (生产环境可移除)
  useEffect(() => {
    if (__DEV__) {
      console.log('📊 会话状态:', {
        visible,
        hasCurrentSession: !!currentSession,
        isCreatingSession,
        sessionsCount: sessions.length,
      });
    }
  }, [visible, currentSession, isCreatingSession, sessions.length]);

  // 判断是否有可用的历史会话（除了当前会话外还有其他会话）
  const hasHistorySession =
    sessions.length > 1 || (sessions.length === 1 && !currentSession);
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.container,
          {
            bottom: insets.bottom + TAB_BAR_HEIGHT + 8, // 底部安全区域 + Tab栏高度 + 额外间距
          },
        ]}
      >
        {/* 模态框内容 - 使用动态安全区域适配 */}
        <View 
          style={[
            styles.modalContent,
            {
              marginTop: Math.max(insets.top, debugStatusBarHeight), // 🎯 动态使用安全区域值，以保底值为最小值
            }
          ]}
        >
          {/* 渐变背景 - 绝对定位作为背景层 */}
          <ImageBackground
            source={require('../../../../../assets/images/modal-background.png')}
            style={styles.gradientSection}
            resizeMode="stretch"
            imageStyle={styles.backgroundImageStyle}
          />

          {/* 标题栏 - 绝对定位在顶部 */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>知识问答</Text>
            <View style={styles.headerActions}>
              {/* 新建会话按钮 */}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleCreateNewSession}
              >
                <Image
                  source={require('../../../../../assets/images/ai-chat/add-button.png')}
                  style={styles.buttonIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity>

              {/* 会话历史按钮 */}
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  !hasHistorySession && styles.actionButtonDisabled,
                ]}
                onPress={hasHistorySession ? handleHistorySession : undefined}
                disabled={!hasHistorySession}
              >
                <Image
                  source={require('../../../../../assets/images/ai-chat/history-button.png')}
                  style={[
                    styles.buttonIcon,
                    !hasHistorySession && styles.buttonIconDisabled,
                  ]}
                  resizeMode="contain"
                />
              </TouchableOpacity>

              {/* 关闭按钮 */}
              <TouchableOpacity onPress={onClose} style={styles.actionButton}>
                <Image
                  source={require('../../../../../assets/images/ai-chat/close-button.png')}
                  style={styles.buttonIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* 聊天内容区域 - 占用全部空间，但顶部预留标题栏空间 */}
          <View style={styles.chatContentWrapper}>
            <ChatContent />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // 容器样式 - 采用绝对定位方案，确保延伸到状态栏区域
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    // bottom值现在通过动态计算传入，适配不同设备的安全区域
    backgroundColor: 'transparent', // 完全透明，无遮罩层
    zIndex: 1000, // 确保在其他内容之上
  },
  modalContent: {
    flex: 1, // 填满容器
    marginHorizontal: 8, // 左右8px间距，与CustomModal保持一致
    // marginTop将通过动态计算传入，使用实际的安全区域值
    backgroundColor: colors.background.primary,
    borderRadius: 24, // 24px圆角，匹配设计稿和CustomModal
    overflow: 'hidden',
    // 阴影效果，与CustomModal保持一致
    shadowColor: '#64677A',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.24,
    shadowRadius: 24,
    elevation: 12,
  },
  gradientSection: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 260, // 对应设计稿的260px高度
    zIndex: 0,
  },
  chatContentWrapper: {
    flex: 1,
    paddingTop: 60, // 为标题栏预留空间，标题栏高度大约60px
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  backgroundImageStyle: {
    width: '100%',
    height: '100%',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: 10,
    zIndex: 2, // 确保在渐变背景之上
    height: 60, // 明确设置标题栏高度
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2A2B33',
    fontFamily: typography.fontFamily, // 使用系统默认字体
    textAlign: 'left',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8, // 增加padding让按钮更大更好点击
    minWidth: 32, // 增加最小尺寸
    minHeight: 32,
  },
  buttonIcon: {
    width: 26,
    height: 26,
  },
  actionButtonDisabled: {
    opacity: 0.3, // 禁用时降低透明度
  },
  buttonIconDisabled: {
    opacity: 0.3, // 禁用时图标也降低透明度
  },
  contentContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  sessionSection: {
    marginBottom: spacing.lg,
  },
  contextSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily,
  },
  chatSection: {
    flex: 1,
    marginBottom: spacing.lg,
  },

  inputSection: {
    minHeight: 60,
  },
});

export default CommonAIChatModal;
